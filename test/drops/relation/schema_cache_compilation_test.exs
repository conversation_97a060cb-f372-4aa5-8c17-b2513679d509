defmodule Drops.Relation.SchemaCacheCompilationTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache

  describe "schema inference during compilation" do
    test "returns fallback schema when cache miss occurs during compilation" do
      # Clear any existing cache
      SchemaCache.clear_all()

      # Simulate cache miss during compilation
      drops_schema =
        SchemaCache.get_or_infer_schema(
          Test.Repo,
          "test_table",
          fn ->
            # This function should not be called during cache miss
            raise "Should not be called during compilation cache miss"
          end
        )

      # Verify we get a fallback schema
      assert drops_schema.source == "test_table"
      assert length(drops_schema.fields) == 1
      assert hd(drops_schema.fields).name == :id
    end

    test "returns cached schema when cache hit occurs" do
      # Clear cache first
      SchemaCache.clear_all()

      # Create a test schema to cache
      test_schema = create_test_schema("cached_table")

      # Cache it manually
      SchemaCache.cache_schema(Test.Repo, "cached_table", test_schema)

      # Now get_or_infer_schema should return the cached version
      drops_schema =
        SchemaCache.get_or_infer_schema(
          Test.Repo,
          "cached_table",
          fn ->
            # This should be called since we have a cache hit
            test_schema
          end
        )

      # Verify we get the cached schema
      assert drops_schema.source == "cached_table"
    end

    test "infer_and_cache_schema performs inference and caches result" do
      # Clear cache first
      SchemaCache.clear_all()

      test_schema = create_test_schema("inference_table")

      # Use infer_and_cache_schema to populate cache
      result =
        SchemaCache.infer_and_cache_schema(
          Test.Repo,
          "inference_table",
          fn -> test_schema end
        )

      # Verify the result
      assert result == test_schema

      # Verify it was cached by checking cache hit
      cached_schema =
        SchemaCache.get_or_infer_schema(
          Test.Repo,
          "inference_table",
          fn -> test_schema end
        )

      # Now we only cache the drops_schema, so compare directly
      assert cached_schema == test_schema
    end
  end

  defp create_test_schema(table_name) do
    alias Drops.Relation.Schema
    alias Drops.Relation.Schema.{Field, PrimaryKey, Indices}

    id_field = Field.new(:id, :integer, :id, :id)
    name_field = Field.new(:name, :string, :string, :name)

    # Only return the Drops.Relation.Schema - no more Ecto AST caching
    Schema.new(
      table_name,
      PrimaryKey.new([id_field]),
      # foreign_keys
      [],
      # fields
      [id_field, name_field],
      Indices.new(),
      # virtual_fields
      []
    )
  end
end
