defmodule Drops.Relation.SchemaCacheTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  # Mock repository for testing
  defmodule TestRepo do
    def query(
          "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1",
          [],
          _opts
        ) do
      # Return a mock migration version
      {:ok, %{rows: [["20231201120000"]]}}
    end

    def query(_, _, _), do: {:error, :not_found}
  end

  # Another mock repo with different migration version
  defmodule TestRepo2 do
    def query(
          "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1",
          [],
          _opts
        ) do
      {:ok, %{rows: [["20231201130000"]]}}
    end

    def query(_, _, _), do: {:error, :not_found}
  end

  # Mock repo with no migrations
  defmodule EmptyRepo do
    def query(
          "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1",
          [],
          _opts
        ) do
      {:ok, %{rows: []}}
    end

    def query(_, _, _), do: {:error, :not_found}
  end

  setup do
    # Clear cache before each test
    if Process.whereis(SchemaCache) do
      SchemaCache.clear_all()
    else
      start_supervised!(SchemaCache)
    end

    # Mock config to enable cache
    original_config = Config.schema_cache()

    on_exit(fn ->
      # Restore original config
      Config.update(:schema_cache, original_config)
    end)

    # Enable cache for tests
    Config.update(:schema_cache,
      enabled: true,
      max_entries: 100,
      cleanup_interval: :never
    )

    :ok
  end

  describe "get_or_infer_schema/3" do
    test "returns cached schema on cache hit" do
      {:ok, inference_count} = Agent.start_link(fn -> 0 end)

      infer_fn = fn ->
        Agent.update(inference_count, &(&1 + 1))
        :mock_drops_schema
      end

      # First call should trigger inference
      result1 = SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      assert result1 == :mock_drops_schema
      assert Agent.get(inference_count, & &1) == 1

      # Second call should use cache
      result2 = SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      assert result2 == :mock_drops_schema
      # Should not increment
      assert Agent.get(inference_count, & &1) == 1

      Agent.stop(inference_count)
    end

    test "triggers inference on cache miss" do
      infer_fn = fn -> :new_drops_schema end

      result = SchemaCache.get_or_infer_schema(TestRepo, "posts", infer_fn)
      assert result == :new_drops_schema
    end

    test "invalidates cache when migration version changes" do
      {:ok, inference_count} = Agent.start_link(fn -> 0 end)

      infer_fn = fn ->
        Agent.update(inference_count, &(&1 + 1))
        :drops_schema_v1
      end

      # Cache with TestRepo (version 20231201120000)
      result1 = SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      assert result1 == :drops_schema_v1
      assert Agent.get(inference_count, & &1) == 1

      # Use TestRepo2 (different version) - should trigger new inference
      infer_fn2 = fn ->
        Agent.update(inference_count, &(&1 + 1))
        :drops_schema_v2
      end

      result2 = SchemaCache.get_or_infer_schema(TestRepo2, "users", infer_fn2)
      assert result2 == :drops_schema_v2
      assert Agent.get(inference_count, & &1) == 2

      Agent.stop(inference_count)
    end

    test "handles repository with no migrations" do
      infer_fn = fn -> :empty_drops_schema end

      result = SchemaCache.get_or_infer_schema(EmptyRepo, "users", infer_fn)
      assert result == :empty_drops_schema
    end

    test "bypasses cache when disabled" do
      # Disable cache
      Config.update(:schema_cache, enabled: false)

      {:ok, inference_count} = Agent.start_link(fn -> 0 end)

      infer_fn = fn ->
        Agent.update(inference_count, &(&1 + 1))
        :uncached_drops_schema
      end

      # Both calls should trigger inference
      result1 = SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      result2 = SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)

      assert result1 == :uncached_drops_schema
      assert result2 == :uncached_drops_schema
      assert Agent.get(inference_count, & &1) == 2

      Agent.stop(inference_count)
    end
  end

  describe "clear_repo_cache/1" do
    test "clears cache for specific repository" do
      infer_fn = fn -> :drops_schema end

      # Cache schemas for both repos
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo2, "users", infer_fn)

      stats_before = SchemaCache.stats()
      assert stats_before.total_entries == 2

      # Clear cache for TestRepo only
      SchemaCache.clear_repo_cache(TestRepo)

      stats_after = SchemaCache.stats()
      assert stats_after.total_entries == 1

      # TestRepo2 cache should still work
      {:ok, inference_count} = Agent.start_link(fn -> 0 end)

      infer_fn_with_counter = fn ->
        Agent.update(inference_count, &(&1 + 1))
        :drops_schema
      end

      SchemaCache.get_or_infer_schema(TestRepo2, "users", infer_fn_with_counter)
      # Should use cache
      assert Agent.get(inference_count, & &1) == 0

      Agent.stop(inference_count)
    end
  end

  describe "clear_all/0" do
    test "clears entire cache" do
      infer_fn = fn -> :drops_schema end

      # Cache multiple schemas
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo, "posts", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo2, "users", infer_fn)

      stats_before = SchemaCache.stats()
      assert stats_before.total_entries == 3

      # Clear all
      SchemaCache.clear_all()

      stats_after = SchemaCache.stats()
      assert stats_after.total_entries == 0
      assert stats_after.hits == 0
      assert stats_after.misses == 0
    end
  end

  describe "stats/0" do
    test "tracks hit and miss statistics" do
      infer_fn = fn -> :drops_schema end

      initial_stats = SchemaCache.stats()
      assert initial_stats.hits == 0
      assert initial_stats.misses == 0
      assert initial_stats.total_entries == 0

      # First call - cache miss
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)

      stats_after_miss = SchemaCache.stats()
      assert stats_after_miss.misses == 1
      assert stats_after_miss.hits == 0
      assert stats_after_miss.total_entries == 1

      # Second call - cache hit
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)

      stats_after_hit = SchemaCache.stats()
      assert stats_after_hit.misses == 1
      assert stats_after_hit.hits == 1
      assert stats_after_hit.total_entries == 1
      assert stats_after_hit.hit_ratio == 50.0
    end

    test "calculates hit ratio correctly" do
      infer_fn = fn -> :drops_schema end

      # 1 miss, 3 hits
      # miss
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      # hit
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      # hit
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      # hit
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)

      stats = SchemaCache.stats()
      assert stats.hits == 3
      assert stats.misses == 1
      assert stats.hit_ratio == 75.0
    end
  end

  describe "cache size management" do
    test "respects max_entries configuration" do
      # Set a small max_entries for testing
      Config.update(:schema_cache, max_entries: 2)

      infer_fn = fn -> :drops_schema end

      # Add entries up to the limit
      SchemaCache.get_or_infer_schema(TestRepo, "table1", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo, "table2", infer_fn)

      stats = SchemaCache.stats()
      assert stats.total_entries == 2

      # Adding more entries should trigger cleanup
      SchemaCache.get_or_infer_schema(TestRepo, "table3", infer_fn)

      # The cache should have cleaned up some entries
      # Note: The exact behavior depends on the cleanup strategy
      stats_after = SchemaCache.stats()
      assert stats_after.total_entries <= 2
    end
  end

  describe "error handling" do
    test "handles repository query errors gracefully" do
      defmodule ErrorRepo do
        def query(_, _, _), do: {:error, :connection_failed}
      end

      infer_fn = fn -> :error_drops_schema end

      # Should not crash and should use version "0"
      result = SchemaCache.get_or_infer_schema(ErrorRepo, "users", infer_fn)
      assert result == :error_drops_schema
    end

    test "handles repository exceptions gracefully" do
      defmodule ExceptionRepo do
        def query(_, _, _), do: raise("Database connection failed")
      end

      infer_fn = fn -> :exception_drops_schema end

      # Should not crash and should use version "0"
      result = SchemaCache.get_or_infer_schema(ExceptionRepo, "users", infer_fn)
      assert result == :exception_drops_schema
    end
  end
end

defmodule Drops.Relation.CacheTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.{Cache, SchemaCache}
  alias Drops.Config

  # Use the same mock repos from the previous test
  alias Drops.Relation.SchemaCacheTest.{TestRepo, TestRepo2}

  setup do
    # Clear cache before each test
    if Process.whereis(SchemaCache) do
      Cache.clear_all()
    else
      start_supervised!(SchemaCache)
    end

    # Enable cache for tests
    original_config = Config.schema_cache()

    on_exit(fn ->
      Config.update(:schema_cache, original_config)
    end)

    Config.update(:schema_cache,
      enabled: true,
      max_entries: 100,
      cleanup_interval: :never
    )

    :ok
  end

  describe "clear/1" do
    test "clears cache for specific repository" do
      # Add some cached entries
      infer_fn = fn -> :drops_schema end
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo2, "users", infer_fn)

      assert SchemaCache.stats().total_entries == 2

      # Clear cache for TestRepo
      Cache.clear(TestRepo)

      assert SchemaCache.stats().total_entries == 1
    end
  end

  describe "clear_all/0" do
    test "clears entire cache" do
      infer_fn = fn -> :drops_schema end
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)
      SchemaCache.get_or_infer_schema(TestRepo, "posts", infer_fn)

      assert SchemaCache.stats().total_entries == 2

      Cache.clear_all()

      assert SchemaCache.stats().total_entries == 0
    end
  end

  describe "stats/0" do
    test "returns cache statistics" do
      stats = Cache.stats()

      assert is_map(stats)
      assert Map.has_key?(stats, :total_entries)
      assert Map.has_key?(stats, :hits)
      assert Map.has_key?(stats, :misses)
      assert Map.has_key?(stats, :hit_ratio)
    end
  end

  describe "enabled?/0" do
    test "returns true when cache is enabled" do
      Config.update(:schema_cache, enabled: true)
      assert Cache.enabled?() == true
    end

    test "returns false when cache is disabled" do
      Config.update(:schema_cache, enabled: false)
      assert Cache.enabled?() == false
    end
  end

  describe "config/0" do
    test "returns current cache configuration" do
      config = Cache.config()

      assert is_list(config)
      assert Keyword.has_key?(config, :enabled)
      assert Keyword.has_key?(config, :max_entries)
      assert Keyword.has_key?(config, :cleanup_interval)
    end
  end

  describe "warm_up/2" do
    test "returns error when cache is disabled" do
      Config.update(:schema_cache, enabled: false)

      result = Cache.warm_up(TestRepo, ["users", "posts"])
      assert result == {:error, :cache_disabled}
    end

    test "returns ok when cache is enabled" do
      # This test is limited because we don't have real table introspection
      # In a real scenario, this would require actual database tables
      result = Cache.warm_up(TestRepo, [])
      assert result == :ok
    end
  end

  describe "refresh/2" do
    test "clears and optionally warms up cache" do
      infer_fn = fn -> :drops_schema end
      SchemaCache.get_or_infer_schema(TestRepo, "users", infer_fn)

      assert SchemaCache.stats().total_entries == 1

      # Refresh without warming up
      result = Cache.refresh(TestRepo)
      assert result == :ok
      assert SchemaCache.stats().total_entries == 0

      # Refresh with warming up (empty list for this test)
      result = Cache.refresh(TestRepo, [])
      assert result == :ok
    end
  end

  describe "inspect/0" do
    test "returns cache inspection data" do
      inspection = Cache.inspect()

      assert is_list(inspection)
      # The current implementation returns a placeholder message
      assert length(inspection) >= 0
    end
  end
end
